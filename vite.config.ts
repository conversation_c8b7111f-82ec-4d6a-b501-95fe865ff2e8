import path from 'path';
import { defineConfig, loadEnv } from 'vite';

export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, '.', '');
    return {
      define: {
        'process.env.API_KEY': JSON.stringify(env.GEMINI_API_KEY),
        'process.env.GEMINI_API_KEY': JSON.stringify(env.GEMINI_API_KEY)
      },
      resolve: {
        alias: {
          '@': path.resolve(__dirname, '.'),
        }
      },
      server: {
        port: 5173,
        host: true,
        fs: {
          strict: false
        },
        middlewareMode: false,
        hmr: {
          port: 5173
        }
      },
      build: {
        target: 'esnext',
        minify: false,
        rollupOptions: {
          output: {
            format: 'es'
          }
        }
      },
      esbuild: {
        target: 'esnext',
        format: 'esm'
      },
      optimizeDeps: {
        include: []
      }
    };
});
