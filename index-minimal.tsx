// Minimal Winia OS - Testing basic functionality
console.log("🔍 Starting minimal Winia OS...");

// Basic DOM elements
const DESKTOP_ELEMENT = document.getElementById('winia-desktop')!;
const START_MENU_APPS_LIST_ELEMENT = document.getElementById('winia-start-menu-apps-list')!;
const START_BUTTON_ELEMENT = document.getElementById('winia-start-button')!;
const START_MENU_ELEMENT = document.getElementById('winia-start-menu')!;

console.log("🔍 DOM elements check:");
console.log("- DESKTOP_ELEMENT:", DESKTOP_ELEMENT);
console.log("- START_MENU_APPS_LIST_ELEMENT:", START_MENU_APPS_LIST_ELEMENT);

// Basic icons (simplified)
const ICONS = {
    NOTEPAD: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSIjNDI4NWY0IiBkPSJNMTQgMmgtNGMtMS4xIDAtMiAuOS0yIDJ2MTZjMCAxLjEuOSAyIDIgMmgxMGMxLjEgMCAyLS45IDItMlY4bC02LTZ6bTQgMThoLThjLS41NSAwLTEtLjQ1LTEtMXMuNDUtMSAxLTFoOGMuNTUgMCAxIC40NSAxIDFzLS40NSAxLTEgMXptMC00aC04Yy0uNTUgMC0xLS40NS0xLTFzLjQ1LTEgMS0xaDhjLjU1IDAgMSAuNDUgMSAxcy0uNDUgMS0xIDF6bTAtNGgtOGMtLjU1IDAtMS0uNDUtMS0xcy40NS0xIDEtMWg4Yy41NSAwIDEgLjQ1IDEgMXMtLjQ1IDEtMSAxeiIvPjwvc3ZnPg==",
    MY_COMPUTER: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSIjNjY2IiBkPSJNMjAgMThoLTJWOWMwLTEuMS0uOS0yLTItMkg4Yy0xLjEgMC0yIC45LTIgMnY5SDRjLTEuMSAwLTIgLjktMiAydjJjMCAxLjEuOSAyIDIgMmgxNmMxLjEgMCAyLS45IDItMnYtMmMwLTEuMS0uOS0yLTItMnpNOCAxMWg4djdIOHYtN3oiLz48L3N2Zz4=",
    RECYCLE_BIN_EMPTY: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSIjNjY2IiBkPSJNNiAxOWMwIDEuMS45IDIgMiAyaDhjMS4xIDAgMi0uOSAyLTJWN0g2djEyek0xOSA0aC0zLjVsLTEtMWgtNWwtMSAxSDVjLS41NSAwLTEgLjQ1LTEgMXMuNDUgMSAxIDFoMTRjLjU1IDAgMS0uNDUgMS0xcy0uNDUtMS0xLTF6Ii8+PC9zdmc+"
};

// Simple app interface
interface GeneratedApp {
    id: string;
    title: string;
    iconDataUrl: string;
    isPreinstalled?: boolean;
}

// Basic apps
const apps: GeneratedApp[] = [
    {
        id: 'notepad',
        title: 'Notepad',
        iconDataUrl: ICONS.NOTEPAD,
        isPreinstalled: true
    },
    {
        id: 'explorer',
        title: 'My Computer',
        iconDataUrl: ICONS.MY_COMPUTER,
        isPreinstalled: true
    },
    {
        id: 'recycle',
        title: 'Recycle Bin',
        iconDataUrl: ICONS.RECYCLE_BIN_EMPTY,
        isPreinstalled: true
    }
];

// Create desktop icon
function createDesktopIcon(app: GeneratedApp, x?: number, y?: number) {
    console.log(`🔍 Creating desktop icon for: ${app.title}`);
    
    const icon = document.createElement('div');
    icon.className = 'desktop-icon';
    icon.style.cssText = `
        position: absolute;
        left: ${x || 50 + Math.random() * 200}px;
        top: ${y || 50 + Math.random() * 200}px;
        width: 64px;
        height: 80px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: white;
        text-align: center;
        font-size: 11px;
        font-family: 'Segoe UI', sans-serif;
        padding: 4px;
        border-radius: 4px;
        user-select: none;
    `;
    
    const img = document.createElement('img');
    img.src = app.iconDataUrl;
    img.style.cssText = 'width: 32px; height: 32px; margin-bottom: 4px;';
    img.alt = app.title;
    
    const label = document.createElement('div');
    label.textContent = app.title;
    label.style.cssText = 'word-wrap: break-word; line-height: 1.2;';
    
    icon.appendChild(img);
    icon.appendChild(label);
    
    icon.onclick = () => {
        console.log(`🔍 Clicked on ${app.title}`);
        alert(`Opening ${app.title}...`);
    };
    
    DESKTOP_ELEMENT.appendChild(icon);
    console.log(`✅ Desktop icon created for: ${app.title}`);
}

// Add app to start menu
function addAppToStartMenu(app: GeneratedApp) {
    console.log(`🔍 Adding to start menu: ${app.title}`);
    
    const li = document.createElement('li');
    li.role = 'menuitem';
    li.className = 'cursor-hand';
    li.style.cssText = `
        display: flex;
        align-items: center;
        padding: 4px 8px;
        cursor: pointer;
    `;
    
    const img = document.createElement('img');
    img.src = app.iconDataUrl;
    img.className = 'start-menu-item-icon';
    img.style.cssText = 'width: 16px; height: 16px; margin-right: 8px;';
    img.alt = '';
    
    const text = document.createTextNode(app.title);
    
    li.appendChild(img);
    li.appendChild(text);
    
    li.onclick = () => {
        console.log(`🔍 Start menu clicked: ${app.title}`);
        alert(`Opening ${app.title} from start menu...`);
        START_MENU_ELEMENT.classList.add('hidden');
    };
    
    START_MENU_APPS_LIST_ELEMENT.appendChild(li);
    console.log(`✅ Start menu item created for: ${app.title}`);
}

// Initialize system
function initializeMinimalWinia() {
    console.log("🚀 Initializing minimal Winia system...");
    
    // Set desktop background
    DESKTOP_ELEMENT.style.background = 'linear-gradient(135deg, #008080 0%, #20b2aa 100%)';
    
    // Create desktop icons
    apps.forEach((app, index) => {
        createDesktopIcon(app, 50 + (index * 80), 50);
        addAppToStartMenu(app);
    });
    
    // Setup start button
    START_BUTTON_ELEMENT.onclick = () => {
        console.log("🔍 Start button clicked");
        START_MENU_ELEMENT.classList.toggle('hidden');
    };
    
    // Close start menu when clicking outside
    document.addEventListener('click', (e) => {
        if (!START_MENU_ELEMENT.contains(e.target as Node) && !START_BUTTON_ELEMENT.contains(e.target as Node)) {
            START_MENU_ELEMENT.classList.add('hidden');
        }
    });
    
    console.log("✅ Minimal Winia system initialized!");
    console.log(`✅ Created ${apps.length} desktop icons`);
    console.log(`✅ Created ${apps.length} start menu items`);
}

// Start the system when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeMinimalWinia);
} else {
    initializeMinimalWinia();
}

console.log("🔍 Minimal Winia script loaded successfully!");
