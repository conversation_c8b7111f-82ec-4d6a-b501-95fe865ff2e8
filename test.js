// Simple test file to check if modules are loading
console.log("🔍 Test JavaScript file loaded successfully!");

// Test basic functionality
document.addEventListener('DOMContentLoaded', () => {
    console.log("🔍 DOM loaded, testing basic functionality...");
    
    const desktop = document.getElementById('winia-desktop');
    if (desktop) {
        console.log("✅ Desktop element found");
        desktop.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        
        // Add a test icon
        const testIcon = document.createElement('div');
        testIcon.style.cssText = `
            position: absolute;
            top: 50px;
            left: 50px;
            width: 64px;
            height: 64px;
            background: white;
            border: 2px solid #333;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            cursor: pointer;
        `;
        testIcon.textContent = '🧪';
        testIcon.title = 'Test Icon - Click me!';
        testIcon.onclick = () => {
            alert('Test icon clicked! Basic functionality is working.');
        };
        desktop.appendChild(testIcon);
        
        console.log("✅ Test icon added to desktop");
    } else {
        console.error("❌ Desktop element not found");
    }
    
    const startButton = document.getElementById('winia-start-button');
    if (startButton) {
        console.log("✅ Start button found");
        startButton.onclick = () => {
            console.log("🔍 Start button clicked");
            alert('Start button works!');
        };
    } else {
        console.error("❌ Start button not found");
    }
});
