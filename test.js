// Minimal Winia OS - JavaScript version
console.log("🔍 Starting minimal Winia OS (JavaScript)...");

// Basic icons
const ICONS = {
    NOTEPAD: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSIjNDI4NWY0IiBkPSJNMTQgMmgtNGMtMS4xIDAtMiAuOS0yIDJ2MTZjMCAxLjEuOSAyIDIgMmgxMGMxLjEgMCAyLS45IDItMlY4bC02LTZ6bTQgMThoLThjLS41NSAwLTEtLjQ1LTEtMXMuNDUtMSAxLTFoOGMuNTUgMCAxIC40NSAxIDFzLS40NSAxLTEgMXptMC00aC04Yy0uNTUgMC0xLS40NS0xLTFzLjQ1LTEgMS0xaDhjLjU1IDAgMSAuNDUgMSAxcy0uNDUgMS0xIDF6bTAtNGgtOGMtLjU1IDAtMS0uNDUtMS0xcy40NS0xIDEtMWg4Yy41NSAwIDEgLjQ1IDEgMXMtLjQ1IDEtMSAxeiIvPjwvc3ZnPg==",
    MY_COMPUTER: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSIjNjY2IiBkPSJNMjAgMThoLTJWOWMwLTEuMS0uOS0yLTItMkg4Yy0xLjEgMC0yIC45LTIgMnY5SDRjLTEuMSAwLTIgLjktMiAydjJjMCAxLjEuOSAyIDIgMmgxNmMxLjEgMCAyLS45IDItMnYtMmMwLTEuMS0uOS0yLTItMnpNOCAxMWg4djdIOHYtN3oiLz48L3N2Zz4=",
    RECYCLE_BIN_EMPTY: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSIjNjY2IiBkPSJNNiAxOWMwIDEuMS45IDIgMiAyaDhjMS4xIDAgMi0uOSAyLTJWN0g2djEyek0xOSA0aC0zLjVsLTEtMWgtNWwtMSAxSDVjLS41NSAwLTEgLjQ1LTEgMXMuNDUgMSAxIDFoMTRjLjU1IDAgMS0uNDUgMS0xcy0uNDUtMS0xLTF6Ii8+PC9zdmc+"
};

// Basic apps
const apps = [
    {
        id: 'notepad',
        title: 'Notepad',
        iconDataUrl: ICONS.NOTEPAD,
        isPreinstalled: true
    },
    {
        id: 'explorer',
        title: 'My Computer',
        iconDataUrl: ICONS.MY_COMPUTER,
        isPreinstalled: true
    },
    {
        id: 'recycle',
        title: 'Recycle Bin',
        iconDataUrl: ICONS.RECYCLE_BIN_EMPTY,
        isPreinstalled: true
    }
];

// Create desktop icon
function createDesktopIcon(app, x, y) {
    console.log(`🔍 Creating desktop icon for: ${app.title}`);

    const icon = document.createElement('div');
    icon.className = 'desktop-icon';
    icon.style.cssText = `
        position: absolute;
        left: ${x || 50 + Math.random() * 200}px;
        top: ${y || 50 + Math.random() * 200}px;
        width: 64px;
        height: 80px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: white;
        text-align: center;
        font-size: 11px;
        font-family: 'Segoe UI', sans-serif;
        padding: 4px;
        border-radius: 4px;
        user-select: none;
        background: rgba(0,0,0,0.1);
    `;

    const img = document.createElement('img');
    img.src = app.iconDataUrl;
    img.style.cssText = 'width: 32px; height: 32px; margin-bottom: 4px;';
    img.alt = app.title;

    const label = document.createElement('div');
    label.textContent = app.title;
    label.style.cssText = 'word-wrap: break-word; line-height: 1.2;';

    icon.appendChild(img);
    icon.appendChild(label);

    icon.onclick = () => {
        console.log(`🔍 Clicked on ${app.title}`);
        alert(`Opening ${app.title}...`);
    };

    const desktop = document.getElementById('winia-desktop');
    desktop.appendChild(icon);
    console.log(`✅ Desktop icon created for: ${app.title}`);
}

// Add app to start menu
function addAppToStartMenu(app) {
    console.log(`🔍 Adding to start menu: ${app.title}`);

    const li = document.createElement('li');
    li.role = 'menuitem';
    li.className = 'cursor-hand';
    li.style.cssText = `
        display: flex;
        align-items: center;
        padding: 4px 8px;
        cursor: pointer;
    `;

    const img = document.createElement('img');
    img.src = app.iconDataUrl;
    img.className = 'start-menu-item-icon';
    img.style.cssText = 'width: 16px; height: 16px; margin-right: 8px;';
    img.alt = '';

    const text = document.createTextNode(app.title);

    li.appendChild(img);
    li.appendChild(text);

    li.onclick = () => {
        console.log(`🔍 Start menu clicked: ${app.title}`);
        alert(`Opening ${app.title} from start menu...`);
        const startMenu = document.getElementById('winia-start-menu');
        startMenu.classList.add('hidden');
    };

    const startMenuList = document.getElementById('winia-start-menu-apps-list');
    startMenuList.appendChild(li);
    console.log(`✅ Start menu item created for: ${app.title}`);
}

// Initialize system
function initializeMinimalWinia() {
    console.log("🚀 Initializing minimal Winia system...");

    // Get DOM elements
    const desktop = document.getElementById('winia-desktop');
    const startButton = document.getElementById('winia-start-button');
    const startMenu = document.getElementById('winia-start-menu');
    const startMenuList = document.getElementById('winia-start-menu-apps-list');

    console.log("🔍 DOM elements check:");
    console.log("- desktop:", desktop);
    console.log("- startButton:", startButton);
    console.log("- startMenu:", startMenu);
    console.log("- startMenuList:", startMenuList);

    if (!desktop || !startButton || !startMenu || !startMenuList) {
        console.error("❌ Missing required DOM elements");
        return;
    }

    // Set desktop background
    desktop.style.background = 'linear-gradient(135deg, #008080 0%, #20b2aa 100%)';

    // Create desktop icons
    apps.forEach((app, index) => {
        createDesktopIcon(app, 50 + (index * 80), 50);
        addAppToStartMenu(app);
    });

    // Setup start button
    startButton.onclick = () => {
        console.log("🔍 Start button clicked");
        startMenu.classList.toggle('hidden');
    };

    // Close start menu when clicking outside
    document.addEventListener('click', (e) => {
        if (!startMenu.contains(e.target) && !startButton.contains(e.target)) {
            startMenu.classList.add('hidden');
        }
    });

    console.log("✅ Minimal Winia system initialized!");
    console.log(`✅ Created ${apps.length} desktop icons`);
    console.log(`✅ Created ${apps.length} start menu items`);
}

// Start the system when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeMinimalWinia);
} else {
    initializeMinimalWinia();
}

console.log("🔍 Minimal Winia script loaded successfully!");
